'use client';

import { useState } from 'react';
import BlogGenerator from '@/components/blog/BlogGenerator';
import BlogManager from '@/components/blog/BlogManager';
import SeriesManager from '@/components/series/SeriesManager';
import AuthorManager from '@/components/author/AuthorManager';
import ConfigStatus from '@/components/ConfigStatus';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { Sparkles, FileText, BookOpen, Users, Settings } from 'lucide-react';

export default function Home() {
  const [generatedBlog, setGeneratedBlog] = useState<any>(null);

  const handleBlogGenerated = (data: any) => {
    setGeneratedBlog(data);
  };

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Header Section */}
        <div className="relative text-center mb-12 sm:mb-16">
          <div className="absolute top-0 right-0 sm:right-4">
            <ThemeToggle />
          </div>

          <div className="inline-flex items-center justify-center p-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full mb-4 sm:mb-6">
            <Sparkles className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 dark:text-blue-400" />
          </div>

          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 dark:from-slate-100 dark:via-blue-100 dark:to-slate-100 bg-clip-text text-transparent mb-4 sm:mb-6 leading-tight px-4">
            AI博文自动生成系统
          </h1>

          <p className="text-lg sm:text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto leading-relaxed px-4">
            智能生成SEO优化的博文内容，支持系列管理和多语言
          </p>

          <div className="mt-6 sm:mt-8 flex flex-wrap justify-center gap-3 sm:gap-4 text-sm text-slate-500 dark:text-slate-400 px-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>AI驱动</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>SEO优化</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span>多语言支持</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="generate" className="max-w-7xl mx-auto">
          <TabsList className="grid w-full grid-cols-5 sm:grid-cols-5 gap-1 p-1 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl shadow-lg overflow-x-auto">
            <TabsTrigger
              value="generate"
              className="flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3"
            >
              <Sparkles className="w-4 h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">生成</span>
            </TabsTrigger>
            <TabsTrigger
              value="manage"
              className="flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3"
            >
              <FileText className="w-4 h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">管理</span>
            </TabsTrigger>
            <TabsTrigger
              value="series"
              className="flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3"
            >
              <BookOpen className="w-4 h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">系列</span>
            </TabsTrigger>
            <TabsTrigger
              value="authors"
              className="flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3"
            >
              <Users className="w-4 h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">作者</span>
            </TabsTrigger>
            <TabsTrigger
              value="config"
              className="flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3"
            >
              <Settings className="w-4 h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">配置</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="mt-8 sm:mt-12">
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8">
              <BlogGenerator onGenerated={handleBlogGenerated} />
            </div>
          </TabsContent>

          <TabsContent value="manage" className="mt-8 sm:mt-12">
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8">
              <BlogManager />
            </div>
          </TabsContent>

          <TabsContent value="series" className="mt-8 sm:mt-12">
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8">
              <SeriesManager authorId="00000000-0000-0000-0000-000000000001" />
            </div>
          </TabsContent>

          <TabsContent value="authors" className="mt-8 sm:mt-12">
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8">
              <AuthorManager />
            </div>
          </TabsContent>

          <TabsContent value="config" className="mt-8 sm:mt-12">
            <div className="bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8">
              <ConfigStatus />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
