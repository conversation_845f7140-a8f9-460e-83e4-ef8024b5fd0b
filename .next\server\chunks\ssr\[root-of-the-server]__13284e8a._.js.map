{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:scale-[1.02] active:scale-[0.98]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-lg hover:bg-primary/90 hover:shadow-xl\",\n        destructive:\n          \"bg-destructive text-white shadow-lg hover:bg-destructive/90 hover:shadow-xl focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40\",\n        outline:\n          \"border bg-background shadow-md hover:bg-accent hover:text-accent-foreground hover:shadow-lg dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-md hover:bg-secondary/80 hover:shadow-lg\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 hover:shadow-md\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,mfACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border py-6 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-10 w-full min-w-0 rounded-xl border bg-transparent px-4 py-2 text-base shadow-md transition-all duration-200 outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm hover:shadow-lg\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:shadow-lg\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kdACA,yGACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-20 w-full rounded-xl border bg-transparent px-4 py-3 text-base shadow-md transition-all duration-200 outline-none focus-visible:ring-[3px] focus-visible:shadow-lg hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-50 md:text-sm resize-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yfACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-xl border bg-transparent px-4 py-2 text-sm whitespace-nowrap shadow-md transition-all duration-200 outline-none focus-visible:ring-[3px] focus-visible:shadow-lg hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-10 data-[size=sm]:h-9 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,u1BACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/types/blog.ts"], "sourcesContent": ["// 博文相关类型定义\n\nexport interface BlogPost {\n  id: string;\n  title: string;\n  content: string;\n  summary: string;\n  language: string;\n  status: 'draft' | 'published' | 'archived';\n  seo_title?: string;\n  seo_description?: string;\n  seo_keywords?: string[];\n  tags?: string[];\n  category?: string;\n  author_id: string;\n  series_id?: string;\n  series_order?: number;\n  created_at: string;\n  updated_at: string;\n  published_at?: string;\n}\n\nexport interface BlogSeries {\n  id: string;\n  name: string;\n  description: string;\n  language: string;\n  author_id: string;\n  posts_count: number;\n  summary: string; // 系列历史总结\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Author {\n  id: string;\n  name: string;\n  bio?: string;\n  avatar_url?: string;\n  email?: string;\n  website?: string;\n  social_links?: {\n    twitter?: string;\n    linkedin?: string;\n    github?: string;\n  };\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface BlogGenerationRequest {\n  type: 'keyword' | 'topic' | 'title';\n  input: string;\n  language: string;\n  series_id?: string;\n  author_id: string;\n  additional_instructions?: string;\n}\n\nexport interface BlogGenerationResponse {\n  title: string;\n  content: string;\n  summary: string;\n  seo_title: string;\n  seo_description: string;\n  seo_keywords: string[];\n  tags: string[];\n  category: string;\n}\n\n\n\nexport type Language = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR' | 'es-ES' | 'fr-FR' | 'de-DE';\n\nexport const SUPPORTED_LANGUAGES: { code: Language; name: string }[] = [\n  { code: 'zh-CN', name: '中文' },\n  { code: 'en-US', name: 'English' },\n  { code: 'ja-JP', name: '日本語' },\n  { code: 'ko-KR', name: '한국어' },\n  { code: 'es-ES', name: 'Español' },\n  { code: 'fr-FR', name: 'Français' },\n  { code: 'de-DE', name: 'Deutsch' },\n];\n"], "names": [], "mappings": "AAAA,WAAW;;;;AA0EJ,MAAM,sBAA0D;IACrE;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAU;IACjC;QAAE,MAAM;QAAS,MAAM;IAAM;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAM;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAU;IACjC;QAAE,MAAM;QAAS,MAAM;IAAW;IAClC;QAAE,MAAM;QAAS,MAAM;IAAU;CAClC", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/blog/BlogGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { SUPPORTED_LANGUAGES } from '@/types/blog';\nimport type { BlogGenerationRequest, Language, BlogSeries } from '@/types/blog';\n\ninterface BlogGeneratorProps {\n  onGenerated?: (blogPost: any) => void;\n}\n\nexport default function BlogGenerator({ onGenerated }: BlogGeneratorProps) {\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [series, setSeries] = useState<BlogSeries[]>([]);\n  const [formData, setFormData] = useState<Partial<BlogGenerationRequest>>({\n    type: 'keyword',\n    language: 'zh-CN',\n    author_id: '00000000-0000-0000-0000-000000000001', // 默认作者ID\n  });\n\n  // 加载系列列表\n  const loadSeries = async () => {\n    try {\n      const response = await fetch(`/api/series?author_id=${formData.author_id}`);\n      const result = await response.json();\n\n      if (result.success) {\n        setSeries(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load series:', error);\n    }\n  };\n\n  useEffect(() => {\n    if (formData.author_id) {\n      loadSeries();\n    }\n  }, [formData.author_id]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.input) {\n      alert('请输入内容');\n      return;\n    }\n\n    setIsGenerating(true);\n    \n    try {\n      const response = await fetch('/api/blog/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Generation failed');\n      }\n\n      if (onGenerated) {\n        onGenerated(result.data);\n      }\n\n      // 重置表单\n      setFormData({\n        ...formData,\n        input: '',\n        additional_instructions: '',\n      });\n\n      alert('博文生成成功！');\n    } catch (error) {\n      console.error('Generation error:', error);\n      alert(`生成失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle>AI博文生成器</CardTitle>\n        <CardDescription>\n          输入关键词、话题或标题，AI将为您生成高质量的博文内容\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* 生成类型选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">生成类型</label>\n            <Select\n              value={formData.type}\n              onValueChange={(value: 'keyword' | 'topic' | 'title') =>\n                setFormData({ ...formData, type: value })\n              }\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"keyword\">关键词</SelectItem>\n                <SelectItem value=\"topic\">话题</SelectItem>\n                <SelectItem value=\"title\">标题</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 输入内容 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">\n              {formData.type === 'keyword' && '关键词'}\n              {formData.type === 'topic' && '话题描述'}\n              {formData.type === 'title' && '文章标题'}\n            </label>\n            <Input\n              value={formData.input || ''}\n              onChange={(e) => setFormData({ ...formData, input: e.target.value })}\n              placeholder={\n                formData.type === 'keyword' ? '例如：人工智能, 机器学习' :\n                formData.type === 'topic' ? '例如：人工智能在医疗领域的应用' :\n                '例如：AI如何改变我们的生活'\n              }\n              required\n            />\n          </div>\n\n          {/* 语言选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">语言</label>\n            <Select\n              value={formData.language}\n              onValueChange={(value: Language) =>\n                setFormData({ ...formData, language: value })\n              }\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {SUPPORTED_LANGUAGES.map((lang) => (\n                  <SelectItem key={lang.code} value={lang.code}>\n                    {lang.name}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 系列选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">博文系列（可选）</label>\n            <Select\n              value={formData.series_id || 'none'}\n              onValueChange={(value) =>\n                setFormData({ ...formData, series_id: value === 'none' ? undefined : value })\n              }\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"选择系列或留空创建独立文章\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"none\">独立文章</SelectItem>\n                {series.map((seriesItem) => (\n                  <SelectItem key={seriesItem.id} value={seriesItem.id}>\n                    {seriesItem.name} ({seriesItem.posts_count} 篇文章)\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 额外指令 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">额外指令（可选）</label>\n            <Textarea\n              value={formData.additional_instructions || ''}\n              onChange={(e) =>\n                setFormData({ ...formData, additional_instructions: e.target.value })\n              }\n              placeholder=\"例如：请重点关注实际应用案例，文章风格要轻松易懂\"\n              rows={3}\n            />\n          </div>\n\n          {/* 提交按钮 */}\n          <Button\n            type=\"submit\"\n            disabled={isGenerating}\n            className=\"w-full\"\n          >\n            {isGenerating ? '正在生成...' : '生成博文'}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAee,SAAS,cAAc,EAAE,WAAW,EAAsB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;QACvE,MAAM;QACN,UAAU;QACV,WAAW;IACb;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,SAAS,SAAS,EAAE;YAC1E,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,SAAS,EAAE;YACtB;QACF;IACF,GAAG;QAAC,SAAS,SAAS;KAAC;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,IAAI,aAAa;gBACf,YAAY,OAAO,IAAI;YACzB;YAEA,OAAO;YACP,YAAY;gBACV,GAAG,QAAQ;gBACX,OAAO;gBACP,yBAAyB;YAC3B;YAEA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,IAAI;oCACpB,eAAe,CAAC,QACd,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM;wCAAM;;sDAGzC,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;wCACd,SAAS,IAAI,KAAK,aAAa;wCAC/B,SAAS,IAAI,KAAK,WAAW;wCAC7B,SAAS,IAAI,KAAK,WAAW;;;;;;;8CAEhC,8OAAC,iIAAA,CAAA,QAAK;oCACJ,OAAO,SAAS,KAAK,IAAI;oCACzB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,aACE,SAAS,IAAI,KAAK,YAAY,kBAC9B,SAAS,IAAI,KAAK,UAAU,oBAC5B;oCAEF,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,QAAQ;oCACxB,eAAe,CAAC,QACd,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU;wCAAM;;sDAG7C,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;sDACX,oHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAiB,OAAO,KAAK,IAAI;8DACzC,KAAK,IAAI;mDADK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,SAAS,IAAI;oCAC7B,eAAe,CAAC,QACd,YAAY;4CAAE,GAAG,QAAQ;4CAAE,WAAW,UAAU,SAAS,YAAY;wCAAM;;sDAG7E,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;gDACxB,OAAO,GAAG,CAAC,CAAC,2BACX,8OAAC,kIAAA,CAAA,aAAU;wDAAqB,OAAO,WAAW,EAAE;;4DACjD,WAAW,IAAI;4DAAC;4DAAG,WAAW,WAAW;4DAAC;;uDAD5B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAStC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,oIAAA,CAAA,WAAQ;oCACP,OAAO,SAAS,uBAAuB,IAAI;oCAC3C,UAAU,CAAC,IACT,YAAY;4CAAE,GAAG,QAAQ;4CAAE,yBAAyB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAErE,aAAY;oCACZ,MAAM;;;;;;;;;;;;sCAKV,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/blog/BlogEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Badge } from '@/components/ui/badge';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport type { BlogPost } from '@/types/blog';\n\ninterface BlogEditorProps {\n  blogPost: BlogPost;\n  onSave?: (updatedPost: Partial<BlogPost>) => void;\n  onPublish?: (postId: string) => void;\n}\n\nexport default function BlogEditor({ blogPost, onSave, onPublish }: BlogEditorProps) {\n  const [editedPost, setEditedPost] = useState<Partial<BlogPost>>({\n    title: blogPost.title,\n    content: blogPost.content,\n    summary: blogPost.summary || '',\n    seo_title: blogPost.seo_title || '',\n    seo_description: blogPost.seo_description || '',\n    seo_keywords: blogPost.seo_keywords || [],\n    tags: blogPost.tags || [],\n    category: blogPost.category || '',\n  });\n  const [isSaving, setIsSaving] = useState(false);\n  const [isPublishing, setIsPublishing] = useState(false);\n\n  // 处理保存\n  const handleSave = async () => {\n    setIsSaving(true);\n    try {\n      if (onSave) {\n        await onSave(editedPost);\n      }\n      alert('保存成功！');\n    } catch (error) {\n      console.error('Save error:', error);\n      alert('保存失败');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // 处理发布\n  const handlePublish = async () => {\n    if (!confirm('确定要发布这篇文章吗？')) {\n      return;\n    }\n\n    setIsPublishing(true);\n    try {\n      if (onPublish) {\n        await onPublish(blogPost.id);\n      }\n      alert('发布成功！');\n    } catch (error) {\n      console.error('Publish error:', error);\n      alert('发布失败');\n    } finally {\n      setIsPublishing(false);\n    }\n  };\n\n  // 处理关键词和标签的编辑\n  const handleKeywordsChange = (value: string) => {\n    const keywords = value.split(',').map(k => k.trim()).filter(k => k);\n    setEditedPost({ ...editedPost, seo_keywords: keywords });\n  };\n\n  const handleTagsChange = (value: string) => {\n    const tags = value.split(',').map(t => t.trim()).filter(t => t);\n    setEditedPost({ ...editedPost, tags });\n  };\n\n  return (\n    <div className=\"max-w-6xl mx-auto space-y-6\">\n      {/* 头部操作栏 */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold\">编辑博文</h1>\n          <p className=\"text-gray-600\">\n            状态: {blogPost.status === 'draft' ? '草稿' : blogPost.status === 'published' ? '已发布' : '已归档'}\n          </p>\n        </div>\n        <div className=\"space-x-2\">\n          <Button variant=\"outline\" onClick={handleSave} disabled={isSaving}>\n            {isSaving ? '保存中...' : '保存草稿'}\n          </Button>\n          {blogPost.status === 'draft' && (\n            <Button onClick={handlePublish} disabled={isPublishing}>\n              {isPublishing ? '发布中...' : '发布文章'}\n            </Button>\n          )}\n        </div>\n      </div>\n\n      <Tabs defaultValue=\"edit\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"edit\">编辑</TabsTrigger>\n          <TabsTrigger value=\"preview\">预览</TabsTrigger>\n          <TabsTrigger value=\"seo\">SEO设置</TabsTrigger>\n        </TabsList>\n\n        {/* 编辑标签页 */}\n        <TabsContent value=\"edit\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>基本信息</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* 标题 */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">标题</label>\n                <Input\n                  value={editedPost.title || ''}\n                  onChange={(e) => setEditedPost({ ...editedPost, title: e.target.value })}\n                  placeholder=\"输入文章标题\"\n                />\n              </div>\n\n              {/* 摘要 */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">摘要</label>\n                <Textarea\n                  value={editedPost.summary || ''}\n                  onChange={(e) => setEditedPost({ ...editedPost, summary: e.target.value })}\n                  placeholder=\"输入文章摘要\"\n                  rows={3}\n                />\n              </div>\n\n              {/* 分类 */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">分类</label>\n                <Input\n                  value={editedPost.category || ''}\n                  onChange={(e) => setEditedPost({ ...editedPost, category: e.target.value })}\n                  placeholder=\"输入文章分类\"\n                />\n              </div>\n\n              {/* 标签 */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">标签（用逗号分隔）</label>\n                <Input\n                  value={(editedPost.tags || []).join(', ')}\n                  onChange={(e) => handleTagsChange(e.target.value)}\n                  placeholder=\"例如：AI, 机器学习, 技术\"\n                />\n                <div className=\"flex flex-wrap gap-1\">\n                  {(editedPost.tags || []).map((tag, index) => (\n                    <Badge key={index} variant=\"outline\">\n                      {tag}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 内容编辑 */}\n          <Card>\n            <CardHeader>\n              <CardTitle>文章内容</CardTitle>\n              <CardDescription>\n                支持Markdown格式，可以使用**粗体**、*斜体*、# 标题等语法\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Textarea\n                value={editedPost.content || ''}\n                onChange={(e) => setEditedPost({ ...editedPost, content: e.target.value })}\n                placeholder=\"在这里输入文章内容...\"\n                rows={20}\n                className=\"font-mono\"\n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* 预览标签页 */}\n        <TabsContent value=\"preview\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>{editedPost.title || '未命名文章'}</CardTitle>\n              {editedPost.summary && (\n                <CardDescription className=\"text-base\">\n                  {editedPost.summary}\n                </CardDescription>\n              )}\n              <div className=\"flex items-center gap-2 text-sm text-gray-500\">\n                {editedPost.category && (\n                  <>\n                    <Badge variant=\"secondary\">{editedPost.category}</Badge>\n                    <span>•</span>\n                  </>\n                )}\n                <span>语言: {blogPost.language}</span>\n                <span>•</span>\n                <span>创建时间: {new Date(blogPost.created_at).toLocaleDateString('zh-CN')}</span>\n              </div>\n              {(editedPost.tags || []).length > 0 && (\n                <div className=\"flex flex-wrap gap-1\">\n                  {(editedPost.tags || []).map((tag, index) => (\n                    <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                      {tag}\n                    </Badge>\n                  ))}\n                </div>\n              )}\n            </CardHeader>\n            <CardContent>\n              <div className=\"prose prose-sm max-w-none\">\n                <ReactMarkdown remarkPlugins={[remarkGfm]}>\n                  {editedPost.content || ''}\n                </ReactMarkdown>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* SEO设置标签页 */}\n        <TabsContent value=\"seo\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>SEO优化设置</CardTitle>\n              <CardDescription>\n                优化搜索引擎排名的相关设置\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* SEO标题 */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">SEO标题</label>\n                <Input\n                  value={editedPost.seo_title || ''}\n                  onChange={(e) => setEditedPost({ ...editedPost, seo_title: e.target.value })}\n                  placeholder=\"SEO优化的标题（建议50-60字符）\"\n                />\n                <p className=\"text-xs text-gray-500\">\n                  当前长度: {(editedPost.seo_title || '').length} 字符\n                </p>\n              </div>\n\n              {/* SEO描述 */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">SEO描述</label>\n                <Textarea\n                  value={editedPost.seo_description || ''}\n                  onChange={(e) => setEditedPost({ ...editedPost, seo_description: e.target.value })}\n                  placeholder=\"SEO描述（建议150-160字符）\"\n                  rows={3}\n                />\n                <p className=\"text-xs text-gray-500\">\n                  当前长度: {(editedPost.seo_description || '').length} 字符\n                </p>\n              </div>\n\n              {/* SEO关键词 */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">SEO关键词（用逗号分隔）</label>\n                <Input\n                  value={(editedPost.seo_keywords || []).join(', ')}\n                  onChange={(e) => handleKeywordsChange(e.target.value)}\n                  placeholder=\"例如：人工智能, 机器学习, 深度学习\"\n                />\n                <div className=\"flex flex-wrap gap-1\">\n                  {(editedPost.seo_keywords || []).map((keyword, index) => (\n                    <Badge key={index} variant=\"secondary\">\n                      {keyword}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAmBe,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAmB;IACjF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAC9D,OAAO,SAAS,KAAK;QACrB,SAAS,SAAS,OAAO;QACzB,SAAS,SAAS,OAAO,IAAI;QAC7B,WAAW,SAAS,SAAS,IAAI;QACjC,iBAAiB,SAAS,eAAe,IAAI;QAC7C,cAAc,SAAS,YAAY,IAAI,EAAE;QACzC,MAAM,SAAS,IAAI,IAAI,EAAE;QACzB,UAAU,SAAS,QAAQ,IAAI;IACjC;IACA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,OAAO;IACP,MAAM,aAAa;QACjB,YAAY;QACZ,IAAI;YACF,IAAI,QAAQ;gBACV,MAAM,OAAO;YACf;YACA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,gBAAgB;YAC3B;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,IAAI,WAAW;gBACb,MAAM,UAAU,SAAS,EAAE;YAC7B;YACA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,cAAc;IACd,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK;QACjE,cAAc;YAAE,GAAG,UAAU;YAAE,cAAc;QAAS;IACxD;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK;QAC7D,cAAc;YAAE,GAAG,UAAU;YAAE;QAAK;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;;oCAAgB;oCACtB,SAAS,MAAM,KAAK,UAAU,OAAO,SAAS,MAAM,KAAK,cAAc,QAAQ;;;;;;;;;;;;;kCAGxF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAY,UAAU;0CACtD,WAAW,WAAW;;;;;;4BAExB,SAAS,MAAM,KAAK,yBACnB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAe,UAAU;0CACvC,eAAe,WAAW;;;;;;;;;;;;;;;;;;0BAMnC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAO,WAAU;;kCAClC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;0CAC1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAM;;;;;;;;;;;;kCAI3B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;;0CAClC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,WAAW,KAAK,IAAI;wDAC3B,UAAU,CAAC,IAAM,cAAc;gEAAE,GAAG,UAAU;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACtE,aAAY;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,WAAW,OAAO,IAAI;wDAC7B,UAAU,CAAC,IAAM,cAAc;gEAAE,GAAG,UAAU;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAKV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,WAAW,QAAQ,IAAI;wDAC9B,UAAU,CAAC,IAAM,cAAc;gEAAE,GAAG,UAAU;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACzE,aAAY;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC;wDACpC,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAChD,aAAY;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACZ,CAAC,WAAW,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,KAAK,sBACjC,8OAAC,iIAAA,CAAA,QAAK;gEAAa,SAAQ;0EACxB;+DADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUtB,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4CACP,OAAO,WAAW,OAAO,IAAI;4CAC7B,UAAU,CAAC,IAAM,cAAc;oDAAE,GAAG,UAAU;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAE,WAAW,KAAK,IAAI;;;;;;wCAC/B,WAAW,OAAO,kBACjB,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,WAAW,OAAO;;;;;;sDAGvB,8OAAC;4CAAI,WAAU;;gDACZ,WAAW,QAAQ,kBAClB;;sEACE,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa,WAAW,QAAQ;;;;;;sEAC/C,8OAAC;sEAAK;;;;;;;;8DAGV,8OAAC;;wDAAK;wDAAK,SAAS,QAAQ;;;;;;;8DAC5B,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAK;wDAAO,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;wCAE/D,CAAC,WAAW,IAAI,IAAI,EAAE,EAAE,MAAM,GAAG,mBAChC,8OAAC;4CAAI,WAAU;sDACZ,CAAC,WAAW,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,KAAK,sBACjC,8OAAC,iIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAU,WAAU;8DAC5C;mDADS;;;;;;;;;;;;;;;;8CAOpB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;4CAAC,eAAe;gDAAC,6IAAA,CAAA,UAAS;6CAAC;sDACtC,WAAW,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAM,WAAU;kCACjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAO,WAAW,SAAS,IAAI;oDAC/B,UAAU,CAAC,IAAM,cAAc;4DAAE,GAAG,UAAU;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC1E,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC5B,CAAC,WAAW,SAAS,IAAI,EAAE,EAAE,MAAM;wDAAC;;;;;;;;;;;;;sDAK/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,8OAAC,oIAAA,CAAA,WAAQ;oDACP,OAAO,WAAW,eAAe,IAAI;oDACrC,UAAU,CAAC,IAAM,cAAc;4DAAE,GAAG,UAAU;4DAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAChF,aAAY;oDACZ,MAAM;;;;;;8DAER,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC5B,CAAC,WAAW,eAAe,IAAI,EAAE,EAAE,MAAM;wDAAC;;;;;;;;;;;;;sDAKrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAO,CAAC,WAAW,YAAY,IAAI,EAAE,EAAE,IAAI,CAAC;oDAC5C,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,aAAY;;;;;;8DAEd,8OAAC;oDAAI,WAAU;8DACZ,CAAC,WAAW,YAAY,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC7C,8OAAC,iIAAA,CAAA,QAAK;4DAAa,SAAQ;sEACxB;2DADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhC", "debugId": null}}, {"offset": {"line": 1801, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/blog/BlogPreview.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Edit, Eye } from 'lucide-react';\nimport type { BlogPost } from '@/types/blog';\n\ninterface BlogPreviewProps {\n  blogPost: BlogPost;\n  generatedContent?: {\n    title: string;\n    content: string;\n    summary: string;\n    seo_title: string;\n    seo_description: string;\n    seo_keywords: string[];\n    tags: string[];\n    category: string;\n  };\n  onEdit?: () => void;\n}\n\nexport default function BlogPreview({ blogPost, generatedContent, onEdit }: BlogPreviewProps) {\n  const content = generatedContent || blogPost;\n\n  // 简单的Markdown渲染（基础版本）\n  const renderMarkdown = (text: string) => {\n    return text\n      .replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-semibold mt-4 mb-2\">$1</h3>')\n      .replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-semibold mt-6 mb-3\">$1</h2>')\n      .replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold mt-8 mb-4\">$1</h1>')\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-semibold\">$1</strong>')\n      .replace(/\\*(.*?)\\*/g, '<em class=\"italic\">$1</em>')\n      .replace(/\\n\\n/g, '</p><p class=\"mb-4\">')\n      .replace(/\\n/g, '<br>')\n      .replace(/^(.*)$/gm, '<p class=\"mb-4\">$1</p>');\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* SEO信息预览 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">SEO信息</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div>\n            <label className=\"text-sm font-medium text-gray-600\">SEO标题</label>\n            <p className=\"text-sm bg-gray-50 p-2 rounded mt-1\">\n              {content.seo_title || content.title}\n            </p>\n          </div>\n          \n          <div>\n            <label className=\"text-sm font-medium text-gray-600\">SEO描述</label>\n            <p className=\"text-sm bg-gray-50 p-2 rounded mt-1\">\n              {content.seo_description || '暂无描述'}\n            </p>\n          </div>\n          \n          <div>\n            <label className=\"text-sm font-medium text-gray-600\">关键词</label>\n            <div className=\"flex flex-wrap gap-1 mt-1\">\n              {(content.seo_keywords || []).map((keyword, index) => (\n                <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                  {keyword}\n                </Badge>\n              ))}\n            </div>\n          </div>\n          \n          <div>\n            <label className=\"text-sm font-medium text-gray-600\">标签</label>\n            <div className=\"flex flex-wrap gap-1 mt-1\">\n              {(content.tags || []).map((tag, index) => (\n                <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                  {tag}\n                </Badge>\n              ))}\n            </div>\n          </div>\n          \n          <div>\n            <label className=\"text-sm font-medium text-gray-600\">分类</label>\n            <p className=\"text-sm bg-gray-50 p-2 rounded mt-1\">\n              {content.category || '未分类'}\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 博文内容预览 */}\n      <Card>\n        <CardHeader>\n          <CardTitle>{content.title}</CardTitle>\n          {content.summary && (\n            <CardDescription className=\"text-base\">\n              {content.summary}\n            </CardDescription>\n          )}\n          <div className=\"flex items-center gap-2 text-sm text-gray-500\">\n            <span>状态: {blogPost.status === 'draft' ? '草稿' : blogPost.status === 'published' ? '已发布' : '已归档'}</span>\n            <span>•</span>\n            <span>语言: {blogPost.language}</span>\n            {blogPost.created_at && (\n              <>\n                <span>•</span>\n                <span>创建时间: {new Date(blogPost.created_at).toLocaleDateString('zh-CN')}</span>\n              </>\n            )}\n          </div>\n        </CardHeader>\n        <CardContent>\n          <div \n            className=\"prose prose-sm max-w-none\"\n            dangerouslySetInnerHTML={{ \n              __html: renderMarkdown(content.content) \n            }}\n          />\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAuBe,SAAS,YAAY,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,EAAoB;IAC1F,MAAM,UAAU,oBAAoB;IAEpC,sBAAsB;IACtB,MAAM,iBAAiB,CAAC;QACtB,OAAO,KACJ,OAAO,CAAC,iBAAiB,uDACzB,OAAO,CAAC,gBAAgB,uDACxB,OAAO,CAAC,eAAe,oDACvB,OAAO,CAAC,kBAAkB,6CAC1B,OAAO,CAAC,cAAc,8BACtB,OAAO,CAAC,SAAS,wBACjB,OAAO,CAAC,OAAO,QACf,OAAO,CAAC,YAAY;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDACV,QAAQ,SAAS,IAAI,QAAQ,KAAK;;;;;;;;;;;;0CAIvC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDACV,QAAQ,eAAe,IAAI;;;;;;;;;;;;0CAIhC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,CAAC,QAAQ,YAAY,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,SAAS,sBAC1C,8OAAC,iIAAA,CAAA,QAAK;gDAAa,SAAQ;gDAAY,WAAU;0DAC9C;+CADS;;;;;;;;;;;;;;;;0CAOlB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,CAAC,QAAQ,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,KAAK,sBAC9B,8OAAC,iIAAA,CAAA,QAAK;gDAAa,SAAQ;gDAAU,WAAU;0DAC5C;+CADS;;;;;;;;;;;;;;;;0CAOlB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDACV,QAAQ,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAE,QAAQ,KAAK;;;;;;4BACxB,QAAQ,OAAO,kBACd,8OAAC,gIAAA,CAAA,kBAAe;gCAAC,WAAU;0CACxB,QAAQ,OAAO;;;;;;0CAGpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK;4CAAK,SAAS,MAAM,KAAK,UAAU,OAAO,SAAS,MAAM,KAAK,cAAc,QAAQ;;;;;;;kDAC1F,8OAAC;kDAAK;;;;;;kDACN,8OAAC;;4CAAK;4CAAK,SAAS,QAAQ;;;;;;;oCAC3B,SAAS,UAAU,kBAClB;;0DACE,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAK;oDAAO,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;kCAKtE,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BACC,WAAU;4BACV,yBAAyB;gCACvB,QAAQ,eAAe,QAAQ,OAAO;4BACxC;;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/blog/BlogManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\n\nimport { Eye, Edit, Trash2, Search, Filter, CheckCircle, Clock, Archive } from 'lucide-react';\nimport BlogEditor from './BlogEditor';\nimport BlogPreview from './BlogPreview';\n\nimport type { BlogPost, Author } from '@/types/blog';\n\nexport default function BlogManager() {\n  const [blogs, setBlogs] = useState<any[]>([]);\n  const [authors, setAuthors] = useState<Author[]>([]);\n  const [filteredBlogs, setFilteredBlogs] = useState<any[]>([]);\n  const [selectedBlog, setSelectedBlog] = useState<any>(null);\n  const [currentView, setCurrentView] = useState<'list' | 'preview' | 'edit'>('list');\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<string>('all');\n  const [languageFilter, setLanguageFilter] = useState<string>('all');\n  const [authorFilter, setAuthorFilter] = useState<string>('all');\n\n\n  // 获取所有博文\n  const fetchBlogs = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (statusFilter !== 'all') params.append('status', statusFilter);\n      if (authorFilter !== 'all') params.append('author_id', authorFilter);\n      if (languageFilter !== 'all') params.append('language', languageFilter);\n\n      const response = await fetch(`/api/blog?${params.toString()}`);\n      const result = await response.json();\n\n      if (result.success) {\n        setBlogs(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch blogs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取作者列表\n  const fetchAuthors = async () => {\n    try {\n      const response = await fetch('/api/authors');\n      const result = await response.json();\n\n      if (result.success) {\n        setAuthors(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch authors:', error);\n    }\n  };\n\n  useEffect(() => {\n    fetchBlogs();\n    fetchAuthors();\n  }, [statusFilter, authorFilter, languageFilter]);\n\n  // 过滤博文（仅用于搜索，其他过滤在API层面处理）\n  useEffect(() => {\n    let filtered = blogs;\n\n    // 搜索过滤\n    if (searchTerm) {\n      filtered = filtered.filter(blog =>\n        blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        blog.content.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        (blog.summary && blog.summary.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n    }\n\n    setFilteredBlogs(filtered);\n  }, [blogs, searchTerm]);\n\n  // 处理预览\n  const handlePreview = (blog: any) => {\n    setSelectedBlog(blog);\n    setCurrentView('preview');\n  };\n\n  // 处理编辑\n  const handleEdit = (blog: any) => {\n    setSelectedBlog(blog);\n    setCurrentView('edit');\n  };\n\n  // 处理保存\n  const handleSave = async (updatedPost: Partial<BlogPost>) => {\n    if (!selectedBlog) return;\n\n    try {\n      const response = await fetch(`/api/blog/${selectedBlog.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updatedPost),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to save');\n      }\n\n      const result = await response.json();\n      \n      // 更新本地状态\n      const updatedBlogs = blogs.map(blog =>\n        blog.id === selectedBlog.id ? { ...blog, ...updatedPost } : blog\n      );\n      setBlogs(updatedBlogs);\n      setSelectedBlog({ ...selectedBlog, ...updatedPost });\n      \n      return result;\n    } catch (error) {\n      console.error('Save error:', error);\n      throw error;\n    }\n  };\n\n  // 处理发布\n  const handlePublish = async (postId: string) => {\n    try {\n      const response = await fetch(`/api/blog/${postId}/publish`, {\n        method: 'POST',\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to publish');\n      }\n\n      const result = await response.json();\n      \n      // 更新本地状态\n      const updatedBlogs = blogs.map(blog =>\n        blog.id === postId\n          ? { ...blog, status: 'published', published_at: new Date().toISOString() }\n          : blog\n      );\n      setBlogs(updatedBlogs);\n\n      if (selectedBlog && selectedBlog.id === postId) {\n        setSelectedBlog({\n          ...selectedBlog,\n          status: 'published',\n          published_at: new Date().toISOString()\n        });\n      }\n      \n      return result;\n    } catch (error) {\n      console.error('Publish error:', error);\n      throw error;\n    }\n  };\n\n  // 处理删除\n  const handleDelete = async (blogId: string) => {\n    if (!confirm('确定要删除这篇文章吗？此操作不可撤销。')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/blog/${blogId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to delete');\n      }\n\n      // 更新本地状态\n      setBlogs(blogs.filter(blog => blog.id !== blogId));\n      \n      // 如果删除的是当前选中的文章，返回列表视图\n      if (selectedBlog && selectedBlog.id === blogId) {\n        setSelectedBlog(null);\n        setCurrentView('list');\n      }\n      \n      alert('文章删除成功！');\n    } catch (error) {\n      console.error('Delete error:', error);\n      alert('删除失败，请重试。');\n    }\n  };\n\n  // 返回列表\n  const handleBackToList = () => {\n    setSelectedBlog(null);\n    setCurrentView('list');\n  };\n\n  // 获取状态徽章\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'published':\n        return <Badge className=\"bg-green-100 text-green-800\"><CheckCircle className=\"w-3 h-3 mr-1\" />已发布</Badge>;\n      case 'draft':\n        return <Badge variant=\"secondary\"><Clock className=\"w-3 h-3 mr-1\" />草稿</Badge>;\n      case 'archived':\n        return <Badge variant=\"outline\"><Archive className=\"w-3 h-3 mr-1\" />已归档</Badge>;\n      default:\n        return <Badge variant=\"secondary\">{status}</Badge>;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"text-lg\">加载中...</div>\n      </div>\n    );\n  }\n\n  // 列表视图\n  if (currentView === 'list') {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h2 className=\"text-2xl font-bold\">文章管理</h2>\n            <p className=\"text-gray-600\">管理所有博文，支持预览、编辑和发布</p>\n          </div>\n          <div className=\"text-sm text-gray-500\">\n            共 {filteredBlogs.length} 篇文章\n          </div>\n        </div>\n\n        {/* 搜索和过滤 */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Filter className=\"w-5 h-5\" />\n              <span>搜索和过滤</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">搜索</label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n                  <Input\n                    placeholder=\"搜索标题、内容或摘要...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">状态</label>\n                <Select value={statusFilter} onValueChange={setStatusFilter}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">全部状态</SelectItem>\n                    <SelectItem value=\"draft\">草稿</SelectItem>\n                    <SelectItem value=\"published\">已发布</SelectItem>\n                    <SelectItem value=\"archived\">已归档</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">作者</label>\n                <Select value={authorFilter} onValueChange={setAuthorFilter}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">全部作者</SelectItem>\n                    {authors.map((author) => (\n                      <SelectItem key={author.id} value={author.id}>\n                        {author.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">语言</label>\n                <Select value={languageFilter} onValueChange={setLanguageFilter}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">全部语言</SelectItem>\n                    <SelectItem value=\"zh-CN\">中文</SelectItem>\n                    <SelectItem value=\"en-US\">英文</SelectItem>\n                    <SelectItem value=\"ja-JP\">日文</SelectItem>\n                    <SelectItem value=\"ko-KR\">韩文</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* 文章列表 */}\n        <div className=\"space-y-4\">\n          {filteredBlogs.map((blog) => (\n            <Card key={blog.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <CardTitle className=\"text-lg mb-2\">{blog.title}</CardTitle>\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      {getStatusBadge(blog.status)}\n                      <Badge variant=\"outline\">{blog.language}</Badge>\n                      {blog.blog_series?.name && (\n                        <Badge variant=\"secondary\">系列: {blog.blog_series.name}</Badge>\n                      )}\n                    </div>\n                    <CardDescription>\n                      {blog.summary || '暂无摘要'}\n                    </CardDescription>\n                  </div>\n\n                  <div className=\"flex space-x-2\">\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handlePreview(blog)}\n                      className=\"flex items-center space-x-1\"\n                    >\n                      <Eye className=\"w-4 h-4\" />\n                      <span>预览</span>\n                    </Button>\n\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handleEdit(blog)}\n                      className=\"flex items-center space-x-1\"\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                      <span>编辑</span>\n                    </Button>\n\n                    {blog.status === 'draft' && (\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handlePublish(blog.id)}\n                        className=\"flex items-center space-x-1\"\n                      >\n                        <CheckCircle className=\"w-4 h-4\" />\n                        <span>发布</span>\n                      </Button>\n                    )}\n\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handleDelete(blog.id)}\n                      className=\"flex items-center space-x-1 text-red-600 hover:text-red-700\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                      <span>删除</span>\n                    </Button>\n                  </div>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                  <div className=\"flex items-center space-x-4\">\n                    <span>作者: {blog.authors?.name}</span>\n                    <span>创建: {new Date(blog.created_at).toLocaleDateString('zh-CN')}</span>\n                    {blog.published_at && (\n                      <span>发布: {new Date(blog.published_at).toLocaleDateString('zh-CN')}</span>\n                    )}\n                  </div>\n\n                  {blog.tags && blog.tags.length > 0 && (\n                    <div className=\"flex space-x-1\">\n                      {blog.tags.slice(0, 3).map((tag: string, index: number) => (\n                        <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                          {tag}\n                        </Badge>\n                      ))}\n                      {blog.tags.length > 3 && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          +{blog.tags.length - 3}\n                        </Badge>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {filteredBlogs.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500\">没有找到匹配的文章</p>\n          </div>\n        )}\n\n\n      </div>\n    );\n  }\n\n  // 预览视图\n  if (currentView === 'preview' && selectedBlog) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <Button variant=\"outline\" onClick={handleBackToList}>\n            ← 返回列表\n          </Button>\n          <Button onClick={() => setCurrentView('edit')}>\n            <Edit className=\"w-4 h-4 mr-2\" />\n            编辑文章\n          </Button>\n        </div>\n        \n        <BlogPreview\n          blogPost={selectedBlog}\n          onEdit={() => setCurrentView('edit')}\n        />\n      </div>\n    );\n  }\n\n  // 编辑视图\n  if (currentView === 'edit' && selectedBlog) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <Button variant=\"outline\" onClick={handleBackToList}>\n            ← 返回列表\n          </Button>\n          <Button variant=\"outline\" onClick={() => setCurrentView('preview')}>\n            <Eye className=\"w-4 h-4 mr-2\" />\n            预览文章\n          </Button>\n        </div>\n        \n        <BlogEditor\n          blogPost={selectedBlog}\n          onSave={handleSave}\n          onPublish={handlePublish}\n        />\n      </div>\n    );\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAZA;;;;;;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAGzD,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI;YACnB,IAAI,iBAAiB,OAAO,OAAO,MAAM,CAAC,UAAU;YACpD,IAAI,iBAAiB,OAAO,OAAO,MAAM,CAAC,aAAa;YACvD,IAAI,mBAAmB,OAAO,OAAO,MAAM,CAAC,YAAY;YAExD,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,QAAQ,IAAI;YAC7D,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;QAAc;QAAc;KAAe;IAE/C,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,OAAO;QACP,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE/E;QAEA,iBAAiB;IACnB,GAAG;QAAC;QAAO;KAAW;IAEtB,OAAO;IACP,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAChB,eAAe;IACjB;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,gBAAgB;QAChB,eAAe;IACjB;IAEA,OAAO;IACP,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,aAAa,EAAE,EAAE,EAAE;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,SAAS;YACT,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,aAAa,EAAE,GAAG;oBAAE,GAAG,IAAI;oBAAE,GAAG,WAAW;gBAAC,IAAI;YAE9D,SAAS;YACT,gBAAgB;gBAAE,GAAG,YAAY;gBAAE,GAAG,WAAW;YAAC;YAElD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM;QACR;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,QAAQ,CAAC,EAAE;gBAC1D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,SAAS;YACT,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAa,cAAc,IAAI,OAAO,WAAW;gBAAG,IACvE;YAEN,SAAS;YAET,IAAI,gBAAgB,aAAa,EAAE,KAAK,QAAQ;gBAC9C,gBAAgB;oBACd,GAAG,YAAY;oBACf,QAAQ;oBACR,cAAc,IAAI,OAAO,WAAW;gBACtC;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,wBAAwB;YACnC;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE;gBAClD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS;YACT,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAE1C,uBAAuB;YACvB,IAAI,gBAAgB,aAAa,EAAE,KAAK,QAAQ;gBAC9C,gBAAgB;gBAChB,eAAe;YACjB;YAEA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,eAAe;IACjB;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAA8B,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAChG,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCAAY,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YACtE,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCAAU,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YACtE;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,OAAO;IACP,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;gCAAwB;gCAClC,cAAc,MAAM;gCAAC;;;;;;;;;;;;;8BAK5B,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAc,eAAe;;kEAC1C,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAc,eAAe;;kEAC1C,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;4DACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAiB,OAAO,OAAO,EAAE;8EACzC,OAAO,IAAI;mEADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAQlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAgB,eAAe;;kEAC5C,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStC,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,gIAAA,CAAA,OAAI;4BAAe,WAAU;;8CAC5B,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAgB,KAAK,KAAK;;;;;;kEAC/C,8OAAC;wDAAI,WAAU;;4DACZ,eAAe,KAAK,MAAM;0EAC3B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW,KAAK,QAAQ;;;;;;4DACtC,KAAK,WAAW,EAAE,sBACjB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;;oEAAY;oEAAK,KAAK,WAAW,CAAC,IAAI;;;;;;;;;;;;;kEAGzD,8OAAC,gIAAA,CAAA,kBAAe;kEACb,KAAK,OAAO,IAAI;;;;;;;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,cAAc;wDAC7B,WAAU;;0EAEV,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAGR,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,WAAW;wDAC1B,WAAU;;0EAEV,8OAAC,2MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;oDAGP,KAAK,MAAM,KAAK,yBACf,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,cAAc,KAAK,EAAE;wDACpC,WAAU;;0EAEV,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAIV,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,aAAa,KAAK,EAAE;wDACnC,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKd,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAK;4DAAK,KAAK,OAAO,EAAE;;;;;;;kEACzB,8OAAC;;4DAAK;4DAAK,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;oDACvD,KAAK,YAAY,kBAChB,8OAAC;;4DAAK;4DAAK,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;4CAI7D,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAa,sBACvC,8OAAC,iIAAA,CAAA,QAAK;4DAAa,SAAQ;4DAAU,WAAU;sEAC5C;2DADS;;;;;oDAIb,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;4DAAU;4DACzC,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;2BAhFxB,KAAK,EAAE;;;;;;;;;;gBA2FrB,cAAc,MAAM,KAAK,mBACxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAOvC;IAEA,OAAO;IACP,IAAI,gBAAgB,aAAa,cAAc;QAC7C,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAkB;;;;;;sCAGrD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,eAAe;;8CACpC,8OAAC,2MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAKrC,8OAAC,yIAAA,CAAA,UAAW;oBACV,UAAU;oBACV,QAAQ,IAAM,eAAe;;;;;;;;;;;;IAIrC;IAEA,OAAO;IACP,IAAI,gBAAgB,UAAU,cAAc;QAC1C,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAkB;;;;;;sCAGrD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,eAAe;;8CACtD,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAKpC,8OAAC,wIAAA,CAAA,UAAU;oBACT,UAAU;oBACV,QAAQ;oBACR,WAAW;;;;;;;;;;;;IAInB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3172, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/series/SeriesManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { SUPPORTED_LANGUAGES } from '@/types/blog';\nimport type { BlogSeries, Language } from '@/types/blog';\n\ninterface SeriesManagerProps {\n  authorId: string;\n  onSeriesSelected?: (series: BlogSeries) => void;\n}\n\nexport default function SeriesManager({ authorId, onSeriesSelected }: SeriesManagerProps) {\n  const [series, setSeries] = useState<BlogSeries[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isCreating, setIsCreating] = useState(false);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [newSeries, setNewSeries] = useState({\n    name: '',\n    description: '',\n    language: 'zh-CN' as Language,\n  });\n\n  // 加载系列列表\n  const loadSeries = async () => {\n    try {\n      const response = await fetch(`/api/series?author_id=${authorId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setSeries(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load series:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadSeries();\n  }, [authorId]);\n\n  // 创建新系列\n  const handleCreateSeries = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!newSeries.name) {\n      alert('请输入系列名称');\n      return;\n    }\n\n    setIsCreating(true);\n    \n    try {\n      const response = await fetch('/api/series', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...newSeries,\n          author_id: authorId,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Creation failed');\n      }\n\n      // 重新加载系列列表\n      await loadSeries();\n      \n      // 重置表单\n      setNewSeries({\n        name: '',\n        description: '',\n        language: 'zh-CN',\n      });\n      \n      setIsDialogOpen(false);\n      alert('系列创建成功！');\n      \n    } catch (error) {\n      console.error('Series creation error:', error);\n      alert(`创建失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <p className=\"text-center text-gray-500\">加载中...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold\">博文系列管理</h2>\n        \n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>创建新系列</Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>创建新系列</DialogTitle>\n              <DialogDescription>\n                创建一个新的博文系列来组织相关的文章\n              </DialogDescription>\n            </DialogHeader>\n            \n            <form onSubmit={handleCreateSeries} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">系列名称</label>\n                <Input\n                  value={newSeries.name}\n                  onChange={(e) => setNewSeries({ ...newSeries, name: e.target.value })}\n                  placeholder=\"例如：AI技术入门系列\"\n                  required\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">系列描述</label>\n                <Textarea\n                  value={newSeries.description}\n                  onChange={(e) => setNewSeries({ ...newSeries, description: e.target.value })}\n                  placeholder=\"描述这个系列的主题和目标\"\n                  rows={3}\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">语言</label>\n                <Select\n                  value={newSeries.language}\n                  onValueChange={(value: Language) =>\n                    setNewSeries({ ...newSeries, language: value })\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {SUPPORTED_LANGUAGES.map((lang) => (\n                      <SelectItem key={lang.code} value={lang.code}>\n                        {lang.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              \n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsDialogOpen(false)}\n                >\n                  取消\n                </Button>\n                <Button type=\"submit\" disabled={isCreating}>\n                  {isCreating ? '创建中...' : '创建'}\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* 系列列表 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {series.map((seriesItem) => (\n          <Card \n            key={seriesItem.id} \n            className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n            onClick={() => onSeriesSelected?.(seriesItem)}\n          >\n            <CardHeader>\n              <CardTitle className=\"text-lg\">{seriesItem.name}</CardTitle>\n              <CardDescription>\n                {seriesItem.description || '暂无描述'}\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex justify-between items-center text-sm text-gray-500\">\n                <span>{seriesItem.posts_count} 篇文章</span>\n                <Badge variant=\"outline\">\n                  {SUPPORTED_LANGUAGES.find(lang => lang.code === seriesItem.language)?.name}\n                </Badge>\n              </div>\n              {seriesItem.summary && (\n                <p className=\"text-sm text-gray-600 mt-2 line-clamp-2\">\n                  {seriesItem.summary}\n                </p>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {series.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <p className=\"text-gray-500 mb-4\">还没有创建任何系列</p>\n            <Button onClick={() => setIsDialogOpen(true)}>\n              创建第一个系列\n            </Button>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAkBe,SAAS,cAAc,EAAE,QAAQ,EAAE,gBAAgB,EAAsB;IACtF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,aAAa;QACb,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,UAAU;YAChE,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,QAAQ;IACR,MAAM,qBAAqB,OAAO;QAChC,EAAE,cAAc;QAEhB,IAAI,CAAC,UAAU,IAAI,EAAE;YACnB,MAAM;YACN;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,SAAS;oBACZ,WAAW;gBACb;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YAEN,OAAO;YACP,aAAa;gBACX,MAAM;gBACN,aAAa;gBACb,UAAU;YACZ;YAEA,gBAAgB;YAChB,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;;;;;IAIjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCAEnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc;;0CACxC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;0CAEV,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAKrB,8OAAC;wCAAK,UAAU;wCAAoB,WAAU;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,UAAU,IAAI;wDACrB,UAAU,CAAC,IAAM,aAAa;gEAAE,GAAG,SAAS;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACnE,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,UAAU,WAAW;wDAC5B,UAAU,CAAC,IAAM,aAAa;gEAAE,GAAG,SAAS;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1E,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,UAAU,QAAQ;wDACzB,eAAe,CAAC,QACd,aAAa;gEAAE,GAAG,SAAS;gEAAE,UAAU;4DAAM;;0EAG/C,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,8OAAC,kIAAA,CAAA,gBAAa;0EACX,oHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC,kIAAA,CAAA,aAAU;wEAAiB,OAAO,KAAK,IAAI;kFACzC,KAAK,IAAI;uEADK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0DAQlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,UAAU;kEAC7B,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,2BACX,8OAAC,gIAAA,CAAA,OAAI;wBAEH,WAAU;wBACV,SAAS,IAAM,mBAAmB;;0CAElC,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW,WAAW,IAAI;;;;;;kDAC/C,8OAAC,gIAAA,CAAA,kBAAe;kDACb,WAAW,WAAW,IAAI;;;;;;;;;;;;0CAG/B,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAM,WAAW,WAAW;oDAAC;;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,oHAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,QAAQ,GAAG;;;;;;;;;;;;oCAGzE,WAAW,OAAO,kBACjB,8OAAC;wCAAE,WAAU;kDACV,WAAW,OAAO;;;;;;;;;;;;;uBAnBpB,WAAW,EAAE;;;;;;;;;;YA2BvB,OAAO,MAAM,KAAK,mBACjB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,gBAAgB;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAQ1D", "debugId": null}}, {"offset": {"line": 3812, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3862, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/author/AuthorManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { User, Mail, Globe, Edit, Trash2 } from 'lucide-react';\nimport type { Author } from '@/types/blog';\n\ninterface AuthorManagerProps {\n  onAuthorSelected?: (author: Author) => void;\n}\n\nexport default function AuthorManager({ onAuthorSelected }: AuthorManagerProps) {\n  const [authors, setAuthors] = useState<Author[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isCreating, setIsCreating] = useState(false);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [editingAuthor, setEditingAuthor] = useState<Author | null>(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    bio: '',\n    email: '',\n    website: '',\n    avatar_url: '',\n    social_links: {\n      twitter: '',\n      linkedin: '',\n      github: ''\n    }\n  });\n\n  // 加载作者列表\n  const loadAuthors = async () => {\n    try {\n      const response = await fetch('/api/authors');\n      const result = await response.json();\n      \n      if (result.success) {\n        setAuthors(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load authors:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadAuthors();\n  }, []);\n\n  // 重置表单\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      bio: '',\n      email: '',\n      website: '',\n      avatar_url: '',\n      social_links: {\n        twitter: '',\n        linkedin: '',\n        github: ''\n      }\n    });\n    setEditingAuthor(null);\n  };\n\n  // 编辑作者\n  const handleEdit = (author: Author) => {\n    setEditingAuthor(author);\n    setFormData({\n      name: author.name,\n      bio: author.bio || '',\n      email: author.email || '',\n      website: author.website || '',\n      avatar_url: author.avatar_url || '',\n      social_links: {\n        twitter: author.social_links?.twitter || '',\n        linkedin: author.social_links?.linkedin || '',\n        github: author.social_links?.github || ''\n      }\n    });\n    setIsDialogOpen(true);\n  };\n\n  // 创建或更新作者\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.name) {\n      alert('请输入作者姓名');\n      return;\n    }\n\n    setIsCreating(true);\n    \n    try {\n      const url = editingAuthor ? `/api/authors/${editingAuthor.id}` : '/api/authors';\n      const method = editingAuthor ? 'PUT' : 'POST';\n      \n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Operation failed');\n      }\n\n      // 重新加载作者列表\n      await loadAuthors();\n      \n      // 重置表单\n      resetForm();\n      setIsDialogOpen(false);\n      \n      alert(editingAuthor ? '作者更新成功！' : '作者创建成功！');\n      \n    } catch (error) {\n      console.error('Author operation error:', error);\n      alert(`操作失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  // 删除作者\n  const handleDelete = async (author: Author) => {\n    if (!confirm(`确定要删除作者\"${author.name}\"吗？这将同时删除该作者的所有文章。`)) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/authors/${author.id}`, {\n        method: 'DELETE',\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Delete failed');\n      }\n\n      // 重新加载作者列表\n      await loadAuthors();\n      alert('作者删除成功！');\n      \n    } catch (error) {\n      console.error('Author deletion error:', error);\n      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <p className=\"text-center text-gray-500\">加载中...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold\">作者管理</h2>\n        \n        <Dialog open={isDialogOpen} onOpenChange={(open) => {\n          setIsDialogOpen(open);\n          if (!open) resetForm();\n        }}>\n          <DialogTrigger asChild>\n            <Button>添加作者</Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl\">\n            <DialogHeader>\n              <DialogTitle>\n                {editingAuthor ? '编辑作者' : '添加新作者'}\n              </DialogTitle>\n              <DialogDescription>\n                {editingAuthor ? '修改作者信息' : '创建一个新的作者档案'}\n              </DialogDescription>\n            </DialogHeader>\n            \n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">姓名 *</label>\n                  <Input\n                    value={formData.name}\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                    placeholder=\"作者姓名\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">邮箱</label>\n                  <Input\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">个人简介</label>\n                <Textarea\n                  value={formData.bio}\n                  onChange={(e) => setFormData({ ...formData, bio: e.target.value })}\n                  placeholder=\"简单介绍一下作者\"\n                  rows={3}\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">网站</label>\n                  <Input\n                    value={formData.website}\n                    onChange={(e) => setFormData({ ...formData, website: e.target.value })}\n                    placeholder=\"https://example.com\"\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">头像URL</label>\n                  <Input\n                    value={formData.avatar_url}\n                    onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}\n                    placeholder=\"https://example.com/avatar.jpg\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">社交媒体</label>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  <Input\n                    value={formData.social_links.twitter}\n                    onChange={(e) => setFormData({\n                      ...formData,\n                      social_links: { ...formData.social_links, twitter: e.target.value }\n                    })}\n                    placeholder=\"Twitter用户名\"\n                  />\n                  <Input\n                    value={formData.social_links.linkedin}\n                    onChange={(e) => setFormData({\n                      ...formData,\n                      social_links: { ...formData.social_links, linkedin: e.target.value }\n                    })}\n                    placeholder=\"LinkedIn用户名\"\n                  />\n                  <Input\n                    value={formData.social_links.github}\n                    onChange={(e) => setFormData({\n                      ...formData,\n                      social_links: { ...formData.social_links, github: e.target.value }\n                    })}\n                    placeholder=\"GitHub用户名\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsDialogOpen(false)}\n                >\n                  取消\n                </Button>\n                <Button type=\"submit\" disabled={isCreating}>\n                  {isCreating ? '保存中...' : editingAuthor ? '更新' : '创建'}\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* 作者列表 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {authors.map((author) => (\n          <Card \n            key={author.id} \n            className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n            onClick={() => onAuthorSelected?.(author)}\n          >\n            <CardHeader className=\"pb-3\">\n              <div className=\"flex items-center space-x-3\">\n                <Avatar>\n                  <AvatarImage src={author.avatar_url || undefined} />\n                  <AvatarFallback>\n                    <User className=\"w-4 h-4\" />\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"flex-1\">\n                  <CardTitle className=\"text-lg\">{author.name}</CardTitle>\n                  {author.email && (\n                    <div className=\"flex items-center text-sm text-gray-500\">\n                      <Mail className=\"w-3 h-3 mr-1\" />\n                      {author.email}\n                    </div>\n                  )}\n                </div>\n                <div className=\"flex space-x-1\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleEdit(author);\n                    }}\n                  >\n                    <Edit className=\"w-3 h-3\" />\n                  </Button>\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleDelete(author);\n                    }}\n                  >\n                    <Trash2 className=\"w-3 h-3\" />\n                  </Button>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent>\n              {author.bio && (\n                <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                  {author.bio}\n                </p>\n              )}\n              \n              <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                <span>\n                  创建于 {new Date(author.created_at).toLocaleDateString('zh-CN')}\n                </span>\n                {author.website && (\n                  <div className=\"flex items-center\">\n                    <Globe className=\"w-3 h-3 mr-1\" />\n                    <span>有网站</span>\n                  </div>\n                )}\n              </div>\n              \n              {(author.social_links?.twitter || author.social_links?.linkedin || author.social_links?.github) && (\n                <div className=\"flex space-x-1 mt-2\">\n                  {author.social_links?.twitter && <Badge variant=\"outline\" className=\"text-xs\">Twitter</Badge>}\n                  {author.social_links?.linkedin && <Badge variant=\"outline\" className=\"text-xs\">LinkedIn</Badge>}\n                  {author.social_links?.github && <Badge variant=\"outline\" className=\"text-xs\">GitHub</Badge>}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {authors.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <p className=\"text-gray-500 mb-4\">还没有添加任何作者</p>\n            <Button onClick={() => setIsDialogOpen(true)}>\n              添加第一个作者\n            </Button>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;;AAiBe,SAAS,cAAc,EAAE,gBAAgB,EAAsB;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,KAAK;QACL,OAAO;QACP,SAAS;QACT,YAAY;QACZ,cAAc;YACZ,SAAS;YACT,UAAU;YACV,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,SAAS;YACT,YAAY;YACZ,cAAc;gBACZ,SAAS;gBACT,UAAU;gBACV,QAAQ;YACV;QACF;QACA,iBAAiB;IACnB;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,iBAAiB;QACjB,YAAY;YACV,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,GAAG,IAAI;YACnB,OAAO,OAAO,KAAK,IAAI;YACvB,SAAS,OAAO,OAAO,IAAI;YAC3B,YAAY,OAAO,UAAU,IAAI;YACjC,cAAc;gBACZ,SAAS,OAAO,YAAY,EAAE,WAAW;gBACzC,UAAU,OAAO,YAAY,EAAE,YAAY;gBAC3C,QAAQ,OAAO,YAAY,EAAE,UAAU;YACzC;QACF;QACA,gBAAgB;IAClB;IAEA,UAAU;IACV,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,MAAM;YACN;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,MAAM,gBAAgB,CAAC,aAAa,EAAE,cAAc,EAAE,EAAE,GAAG;YACjE,MAAM,SAAS,gBAAgB,QAAQ;YAEvC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YAEN,OAAO;YACP;YACA,gBAAgB;YAEhB,MAAM,gBAAgB,YAAY;QAEpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,cAAc;QAChB;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG;YACxD;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;gBACxD,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YACN,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;;;;;IAIjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCAEnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc,CAAC;4BACzC,gBAAgB;4BAChB,IAAI,CAAC,MAAM;wBACb;;0CACE,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;0CAEV,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DACT,gBAAgB,SAAS;;;;;;0DAE5B,8OAAC,kIAAA,CAAA,oBAAiB;0DACf,gBAAgB,WAAW;;;;;;;;;;;;kDAIhC,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACjE,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAIZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAClE,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,SAAS,GAAG;wDACnB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAChE,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,OAAO;gEACvB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACpE,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACvE,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,YAAY,CAAC,OAAO;gEACpC,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,cAAc;4EAAE,GAAG,SAAS,YAAY;4EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACpE;gEACA,aAAY;;;;;;0EAEd,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,YAAY,CAAC,QAAQ;gEACrC,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,cAAc;4EAAE,GAAG,SAAS,YAAY;4EAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACrE;gEACA,aAAY;;;;;;0EAEd,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,YAAY,CAAC,MAAM;gEACnC,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,cAAc;4EAAE,GAAG,SAAS,YAAY;4EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACnE;gEACA,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,UAAU;kEAC7B,aAAa,WAAW,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS5D,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;wBAEH,WAAU;wBACV,SAAS,IAAM,mBAAmB;;0CAElC,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,OAAO,UAAU,IAAI;;;;;;8DACvC,8OAAC,kIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,OAAO,IAAI;;;;;;gDAC1C,OAAO,KAAK,kBACX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,OAAO,KAAK;;;;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,WAAW;oDACb;8DAEA,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,aAAa;oDACf;8DAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC,gIAAA,CAAA,cAAW;;oCACT,OAAO,GAAG,kBACT,8OAAC;wCAAE,WAAU;kDACV,OAAO,GAAG;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAK;oDACC,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB,CAAC;;;;;;;4CAErD,OAAO,OAAO,kBACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;oCAKX,CAAC,OAAO,YAAY,EAAE,WAAW,OAAO,YAAY,EAAE,YAAY,OAAO,YAAY,EAAE,MAAM,mBAC5F,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,YAAY,EAAE,yBAAW,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;4CAC7E,OAAO,YAAY,EAAE,0BAAY,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;4CAC9E,OAAO,YAAY,EAAE,wBAAU,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;uBApE9E,OAAO,EAAE;;;;;;;;;;YA4EnB,QAAQ,MAAM,KAAK,mBAClB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,gBAAgB;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAQ1D", "debugId": null}}, {"offset": {"line": 4668, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ConfigStatus.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { CheckCircle, XCircle, AlertCircle, RefreshCw, Settings, Database, Bot } from 'lucide-react';\n\nexport default function ConfigStatus() {\n  const [qwenConfigured, setQwenConfigured] = useState<boolean | null>(null);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  // 检查环境变量配置状态\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n  const isSupabaseConfigured = supabaseUrl &&\n    supabaseAnonKey &&\n    supabaseUrl !== 'your_supabase_url_here' &&\n    supabaseAnonKey !== 'your_supabase_anon_key_here';\n\n  // 检查配置状态\n  const checkConfigurations = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await fetch('/api/config/check');\n      const result = await response.json();\n      setQwenConfigured(result.qwen_configured);\n    } catch (error) {\n      console.error('Failed to check config:', error);\n      setQwenConfigured(false);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  useEffect(() => {\n    checkConfigurations();\n  }, []);\n\n  const configs = [\n    {\n      name: 'Supabase数据库',\n      icon: <Database className=\"w-5 h-5\" />,\n      status: isSupabaseConfigured ? 'configured' : 'not_configured',\n      description: isSupabaseConfigured ? '数据库连接已配置，可以存储和管理博文数据' : '需要配置Supabase URL和API密钥以使用数据库功能',\n      required: true,\n      details: isSupabaseConfigured ? [\n        `URL: ${supabaseUrl?.substring(0, 30)}...`,\n        '匿名密钥已配置',\n        '数据库表结构正常'\n      ] : [\n        '未配置数据库连接',\n        '无法存储博文数据',\n        '需要设置环境变量'\n      ]\n    },\n    {\n      name: 'Qwen API',\n      icon: <Bot className=\"w-5 h-5\" />,\n      status: qwenConfigured === null ? 'checking' : (qwenConfigured ? 'configured' : 'not_configured'),\n      description: qwenConfigured === null ? '正在检查配置...' :\n                  (qwenConfigured ? 'Qwen API密钥已配置，可以使用AI生成功能' : '需要配置Qwen API密钥以使用AI生成功能'),\n      required: true,\n      details: qwenConfigured === null ? ['检查中...'] :\n               (qwenConfigured ? [\n                 '模型: qwen-plus-latest',\n                 'API连接正常',\n                 '支持博文生成和SEO优化'\n               ] : [\n                 '未配置API密钥',\n                 '无法使用AI生成功能',\n                 '需要设置QWEN_API_KEY'\n               ])\n    }\n  ];\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'configured':\n        return <CheckCircle className=\"w-5 h-5 text-green-600\" />;\n      case 'not_configured':\n        return <XCircle className=\"w-5 h-5 text-red-600\" />;\n      case 'checking':\n        return <AlertCircle className=\"w-5 h-5 text-blue-600 animate-pulse\" />;\n      default:\n        return <AlertCircle className=\"w-5 h-5 text-yellow-600\" />;\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'configured':\n        return <Badge className=\"bg-green-100 text-green-800\">已配置</Badge>;\n      case 'not_configured':\n        return <Badge variant=\"destructive\">未配置</Badge>;\n      case 'checking':\n        return <Badge variant=\"secondary\" className=\"animate-pulse\">检查中...</Badge>;\n      default:\n        return <Badge variant=\"secondary\">未知</Badge>;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold\">系统配置</h2>\n          <p className=\"text-gray-600\">管理系统配置和服务状态</p>\n        </div>\n        <Button\n          onClick={checkConfigurations}\n          disabled={isRefreshing}\n          className=\"flex items-center space-x-2\"\n        >\n          <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />\n          <span>刷新状态</span>\n        </Button>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Settings className=\"w-5 h-5\" />\n            <span>服务配置状态</span>\n          </CardTitle>\n          <CardDescription>\n            系统依赖的核心服务配置状态检查\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-6\">\n            {configs.map((config, index) => (\n              <div key={index} className=\"border rounded-lg p-4\">\n                <div className=\"flex items-start justify-between mb-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      {config.icon}\n                      {getStatusIcon(config.status)}\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-lg\">{config.name}</h4>\n                      <p className=\"text-sm text-gray-600 mt-1\">{config.description}</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    {config.required && (\n                      <Badge variant=\"outline\" className=\"text-xs\">必需</Badge>\n                    )}\n                    {getStatusBadge(config.status)}\n                  </div>\n                </div>\n\n                <div className=\"ml-10 space-y-1\">\n                  {config.details.map((detail, detailIndex) => (\n                    <div key={detailIndex} className=\"text-sm text-gray-500 flex items-center space-x-2\">\n                      <span className=\"w-1 h-1 bg-gray-400 rounded-full\"></span>\n                      <span>{detail}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 配置指南 */}\n      <Card>\n        <CardHeader>\n          <CardTitle>配置指南</CardTitle>\n          <CardDescription>\n            如何正确配置系统所需的服务\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-6\">\n            <div>\n              <h4 className=\"font-semibold mb-3 flex items-center space-x-2\">\n                <Database className=\"w-4 h-4\" />\n                <span>Supabase 数据库配置</span>\n              </h4>\n              <div className=\"space-y-2 text-sm text-gray-600 ml-6\">\n                <p>1. 访问 <a href=\"https://supabase.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 underline\">Supabase官网</a> 创建新项目</p>\n                <p>2. 在项目设置中获取 URL 和 anon key</p>\n                <p>3. 在 <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">.env.local</code> 文件中设置：</p>\n                <div className=\"bg-gray-50 p-3 rounded-lg font-mono text-xs\">\n                  <div>NEXT_PUBLIC_SUPABASE_URL=your_supabase_url</div>\n                  <div>NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key</div>\n                  <div>SUPABASE_SERVICE_ROLE_KEY=your_service_role_key</div>\n                </div>\n                <p>4. 执行 <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">database/init.sql</code> 创建数据库表</p>\n                <p>5. 重启开发服务器</p>\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"font-semibold mb-3 flex items-center space-x-2\">\n                <Bot className=\"w-4 h-4\" />\n                <span>Qwen API 配置</span>\n              </h4>\n              <div className=\"space-y-2 text-sm text-gray-600 ml-6\">\n                <p>1. 访问 <a href=\"https://dashscope.aliyuncs.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 underline\">阿里云DashScope</a> 获取API密钥</p>\n                <p>2. 在 <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">.env.local</code> 文件中设置：</p>\n                <div className=\"bg-gray-50 p-3 rounded-lg font-mono text-xs\">\n                  <div>QWEN_API_KEY=sk-your_qwen_api_key</div>\n                  <div>QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1</div>\n                </div>\n                <p>3. 重启开发服务器</p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,aAAa;IACb,MAAM;IACN,MAAM;IAEN,MAAM,uBAAuB,eAC3B,mBACA,gBAAgB,4BAChB,oBAAoB;IAEtB,SAAS;IACT,MAAM,sBAAsB;QAC1B,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,kBAAkB,OAAO,eAAe;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,kBAAkB;QACpB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,UAAU;QACd;YACE,MAAM;YACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,QAAQ,uCAAuB,eAAe;YAC9C,aAAa,uCAAuB,yBAAyB;YAC7D,UAAU;YACV,SAAS,uCAAuB;gBAC9B,CAAC,KAAK,EAAE,aAAa,UAAU,GAAG,IAAI,GAAG,CAAC;gBAC1C;gBACA;aACD,GAAG;QAKN;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,QAAQ,mBAAmB,OAAO,aAAc,iBAAiB,eAAe;YAChF,aAAa,mBAAmB,OAAO,cAC1B,iBAAiB,6BAA6B;YAC3D,UAAU;YACV,SAAS,mBAAmB,OAAO;gBAAC;aAAS,GACnC,iBAAiB;gBAChB;gBACA;gBACA;aACD,GAAG;gBACF;gBACA;gBACA;aACD;QACZ;KACD;IAED,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,WAAU;8BAAgB;;;;;;YAC9D;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;QACtC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;0CACrE,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAIV,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,OAAO,IAAI;gEACX,cAAc,OAAO,MAAM;;;;;;;sEAE9B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAyB,OAAO,IAAI;;;;;;8EAClD,8OAAC;oEAAE,WAAU;8EAA8B,OAAO,WAAW;;;;;;;;;;;;;;;;;;8DAGjE,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,QAAQ,kBACd,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAU;;;;;;wDAE9C,eAAe,OAAO,MAAM;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC3B,8OAAC;oDAAsB,WAAU;;sEAC/B,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;sEAAM;;;;;;;mDAFC;;;;;;;;;;;mCAtBN;;;;;;;;;;;;;;;;;;;;;0BAmClB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;sEAAM,8OAAC;4DAAE,MAAK;4DAAuB,QAAO;4DAAS,KAAI;4DAAsB,WAAU;sEAA0B;;;;;;wDAAc;;;;;;;8DACpI,8OAAC;8DAAE;;;;;;8DACH,8OAAC;;wDAAE;sEAAK,8OAAC;4DAAK,WAAU;sEAAwC;;;;;;wDAAiB;;;;;;;8DACjF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;;;;;;;8DAEP,8OAAC;;wDAAE;sEAAM,8OAAC;4DAAK,WAAU;sEAAwC;;;;;;wDAAwB;;;;;;;8DACzF,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;8CAIP,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;sEAAM,8OAAC;4DAAE,MAAK;4DAAiC,QAAO;4DAAS,KAAI;4DAAsB,WAAU;sEAA0B;;;;;;wDAAgB;;;;;;;8DAChJ,8OAAC;;wDAAE;sEAAK,8OAAC;4DAAK,WAAU;sEAAwC;;;;;;wDAAiB;;;;;;;8DACjF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;;;;;;;8DAEP,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 5372, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <Button variant=\"ghost\" size=\"icon\" className=\"w-10 h-10\">\n        <div className=\"w-4 h-4\" />\n      </Button>\n    )\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n      className=\"w-10 h-10 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105\"\n    >\n      <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n      <span className=\"sr-only\">切换主题</span>\n    </Button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AANA;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,WAAc,CAAC;IAE7C,qMAAA,CAAA,YAAe,CAAC;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;YAAO,WAAU;sBAC5C,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;0BAEV,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BACf,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 5450, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport BlogGenerator from '@/components/blog/BlogGenerator';\nimport BlogManager from '@/components/blog/BlogManager';\nimport SeriesManager from '@/components/series/SeriesManager';\nimport AuthorManager from '@/components/author/AuthorManager';\nimport ConfigStatus from '@/components/ConfigStatus';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\nimport { Sparkles, FileText, BookOpen, Users, Settings } from 'lucide-react';\n\nexport default function Home() {\n  const [generatedBlog, setGeneratedBlog] = useState<any>(null);\n\n  const handleBlogGenerated = (data: any) => {\n    setGeneratedBlog(data);\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12\">\n        {/* Header Section */}\n        <div className=\"relative text-center mb-12 sm:mb-16\">\n          <div className=\"absolute top-0 right-0 sm:right-4\">\n            <ThemeToggle />\n          </div>\n\n          <div className=\"inline-flex items-center justify-center p-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full mb-4 sm:mb-6\">\n            <Sparkles className=\"w-6 h-6 sm:w-8 sm:h-8 text-blue-600 dark:text-blue-400\" />\n          </div>\n\n          <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 dark:from-slate-100 dark:via-blue-100 dark:to-slate-100 bg-clip-text text-transparent mb-4 sm:mb-6 leading-tight px-4\">\n            AI博文自动生成系统\n          </h1>\n\n          <p className=\"text-lg sm:text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto leading-relaxed px-4\">\n            智能生成SEO优化的博文内容，支持系列管理和多语言\n          </p>\n\n          <div className=\"mt-6 sm:mt-8 flex flex-wrap justify-center gap-3 sm:gap-4 text-sm text-slate-500 dark:text-slate-400 px-4\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>AI驱动</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n              <span>SEO优化</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full animate-pulse\"></div>\n              <span>多语言支持</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Main Content */}\n        <Tabs defaultValue=\"generate\" className=\"max-w-7xl mx-auto\">\n          <TabsList className=\"grid w-full grid-cols-5 sm:grid-cols-5 gap-1 p-1 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl shadow-lg overflow-x-auto\">\n            <TabsTrigger\n              value=\"generate\"\n              className=\"flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3\"\n            >\n              <Sparkles className=\"w-4 h-4 flex-shrink-0\" />\n              <span className=\"text-xs sm:text-sm truncate\">生成</span>\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"manage\"\n              className=\"flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3\"\n            >\n              <FileText className=\"w-4 h-4 flex-shrink-0\" />\n              <span className=\"text-xs sm:text-sm truncate\">管理</span>\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"series\"\n              className=\"flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3\"\n            >\n              <BookOpen className=\"w-4 h-4 flex-shrink-0\" />\n              <span className=\"text-xs sm:text-sm truncate\">系列</span>\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"authors\"\n              className=\"flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3\"\n            >\n              <Users className=\"w-4 h-4 flex-shrink-0\" />\n              <span className=\"text-xs sm:text-sm truncate\">作者</span>\n            </TabsTrigger>\n            <TabsTrigger\n              value=\"config\"\n              className=\"flex items-center justify-center gap-1 sm:gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:shadow-md rounded-xl transition-all duration-200 min-w-0 px-2 sm:px-3\"\n            >\n              <Settings className=\"w-4 h-4 flex-shrink-0\" />\n              <span className=\"text-xs sm:text-sm truncate\">配置</span>\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"generate\" className=\"mt-8 sm:mt-12\">\n            <div className=\"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8\">\n              <BlogGenerator onGenerated={handleBlogGenerated} />\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"manage\" className=\"mt-8 sm:mt-12\">\n            <div className=\"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8\">\n              <BlogManager />\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"series\" className=\"mt-8 sm:mt-12\">\n            <div className=\"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8\">\n              <SeriesManager authorId=\"00000000-0000-0000-0000-000000000001\" />\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"authors\" className=\"mt-8 sm:mt-12\">\n            <div className=\"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8\">\n              <AuthorManager />\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"config\" className=\"mt-8 sm:mt-12\">\n            <div className=\"bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-2xl sm:rounded-3xl border border-slate-200/50 dark:border-slate-700/50 shadow-xl p-4 sm:p-6 lg:p-8\">\n              <ConfigStatus />\n            </div>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAExD,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2IAAA,CAAA,cAAW;;;;;;;;;;sCAGd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAGtB,8OAAC;4BAAG,WAAU;sCAAyO;;;;;;sCAIvP,8OAAC;4BAAE,WAAU;sCAA+F;;;;;;sCAI5G,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAW,WAAU;;sCACtC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;8CAEhD,8OAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;8CAEhD,8OAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;8CAEhD,8OAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;8CAEhD,8OAAC,gIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAIlD,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2IAAA,CAAA,UAAa;oCAAC,aAAa;;;;;;;;;;;;;;;;sCAIhC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,yIAAA,CAAA,UAAW;;;;;;;;;;;;;;;sCAIhB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6IAAA,CAAA,UAAa;oCAAC,UAAS;;;;;;;;;;;;;;;;sCAI5B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6IAAA,CAAA,UAAa;;;;;;;;;;;;;;;sCAIlB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}]}